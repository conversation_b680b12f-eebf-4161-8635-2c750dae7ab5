
'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import FlightSearch from '@/components/FlightSearch';
import { useRouter } from 'next/navigation';
import { flightService, FlightSearchFormData, transformSearchListResponseToFlights, loadRoundTripData, loadOneWayData, isInternationalRoute, parsePassengerCounts } from '@/app/api/services/flightService';

// Import new flight components
import OneWayList from './results/components/one-way-list/OneWayList';
import RoundTripDomesticList from './results/components/round-trip-domestic-list/RoundTripdomesticList';
import RoundTripInternationalList from './results/components/round-trip-international-list/RoundTripinternationalList';

// Import flight models
import { FlightOptionOneWay, FlightOption, FlightOptionInternational } from '@/app/models/flight-list.model';


import { airportService } from '@/app/api/services/airportService';

function FlightSearchResults() {
  const searchParams = useSearchParams();

  // New state for API model flights
  const [oneWayFlights, setOneWayFlights] = useState<FlightOptionOneWay[]>([]);
  const [domesticOnwardFlights, setDomesticOnwardFlights] = useState<FlightOption[]>([]);
  const [domesticReturnFlights, setDomesticReturnFlights] = useState<FlightOption[]>([]);
  const [internationalOnwardFlights, setInternationalOnwardFlights] = useState<FlightOptionInternational[]>([]);
  const [internationalReturnFlights, setInternationalReturnFlights] = useState<FlightOptionInternational[]>([]);

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dataSource, setDataSource] = useState<string>('api'); // Track data source for debugging
  const [showModifyPopup, setShowModifyPopup] = useState(false);
  const [filters, setFilters] = useState({
    priceRange: [2000, 25000], // Increased upper limit to accommodate dummy data prices
    airlines: [],
    departure: [],
    arrival: [],
    stops: []
  });

  // Parse search parameters
  const from = searchParams.get('from') || 'DEL'; // Airport code
  const to = searchParams.get('to') || 'BLR'; // Airport code
  const fromName = searchParams.get('fromName') || 'Delhi'; // Display name
  const toName = searchParams.get('toName') || 'Bengaluru'; // Display name
  const departDate = searchParams.get('departDate') || searchParams.get('depart') || '31 Jul\'25';
  const returnDate = searchParams.get('return');
  const travelers = searchParams.get('travelers') || '1';
  const travelClass = searchParams.get('travelClass') || searchParams.get('class') || 'economy';
  const tripType = (searchParams.get('tripType') || 'oneWay').replace('-', ''); // Handle both 'one-way' and 'oneWay'
  const fareType = searchParams.get('fareType') || 'ON';
  const isRoundTrip = tripType === 'roundTrip' && returnDate;

  // Set view mode based on route type


  // Debug: Log all search parameters
  console.log('🔍 Flight search parameters:', {
    from, to, fromName, toName, departDate, returnDate,
    travelers, travelClass, tripType, fareType, isRoundTrip
  });







  // Function to parse date from search params format
  const parseDateFromSearchParams = (dateStr: string): string => {
    try {
      console.log('📅 Parsing date:', dateStr);

      // Handle ISO format like "2024-01-15" (already in correct format)
      if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
        console.log('✅ Date already in ISO format:', dateStr);
        return dateStr;
      }

      // Handle format like "31 Jul'25" or "01 Aug'25"
      const parts = dateStr.split(' ');
      if (parts.length === 2) {
        const day = parts[0].padStart(2, '0');
        const monthYear = parts[1];
        const month = monthYear.substring(0, 3);
        const year = monthYear.substring(4);

        const monthMap: { [key: string]: string } = {
          'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04',
          'May': '05', 'Jun': '06', 'Jul': '07', 'Aug': '08',
          'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
        };

        const monthNum = monthMap[month] || '01';
        const fullYear = `20${year}`;
        const parsedDate = `${fullYear}-${monthNum}-${day}`;

        console.log('✅ Parsed date from custom format:', parsedDate);
        return parsedDate;
      }

      // Fallback to current date
      console.warn('⚠️ Could not parse date, using current date');
      return new Date().toISOString().split('T')[0];
    } catch (error) {
      console.error('❌ Error parsing date:', error);
      return new Date().toISOString().split('T')[0];
    }
  };

  // Function to get cabin class code
  const getCabinCode = (classStr: string): 'E' | 'PE' | 'B' | 'F' => {
    switch (classStr.toLowerCase()) {
      case 'business':
        return 'B';
      case 'first':
        return 'F';
      case 'premiumeconomy':
      case 'premium economy':
        return 'PE'; // Premium Economy
      default:
        return 'E'; // Economy
    }
  };

  // Load flight search results
  useEffect(() => {
    const searchFlights = async () => {
      if (!from || !to || !departDate) {
        setError('Missing required search parameters');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Get airport data for from and to using airport codes
        console.log('🛫 Looking up airports:', { from, to });
        console.log('🔍 Airport lookup process starting...');

        const fromAirport = await airportService.getAirportByCode(from);
        console.log('✈️ From airport lookup result:', { code: from, result: fromAirport });

        const toAirport = await airportService.getAirportByCode(to);
        console.log('🛬 To airport lookup result:', { code: to, result: toAirport });

        console.log('📊 Final airport lookup results:', {
          from: from,
          to: to,
          fromAirport: fromAirport,
          toAirport: toAirport,
          fromFound: !!fromAirport,
          toFound: !!toAirport
        });

        if (!fromAirport || !toAirport) {
          console.error('❌ Airport lookup failed - this will prevent search API call:', {
            from,
            to,
            fromAirport,
            toAirport,
            fromMissing: !fromAirport,
            toMissing: !toAirport
          });
          throw new Error(`Airport not found: ${!fromAirport ? from : ''} ${!toAirport ? to : ''}`);
        }

        console.log('✅ Airport lookup successful, proceeding with search API call');

        // Parse passenger counts from URL parameters
        const passengerCounts = parsePassengerCounts(searchParams);

        console.log('📊 Parsed passenger counts:', passengerCounts);

        // Prepare search form data
        const searchFormData: FlightSearchFormData = {
          from: fromAirport,
          to: toAirport,
          departureDate: parseDateFromSearchParams(departDate),
          returnDate: returnDate ? parseDateFromSearchParams(returnDate) : undefined,
          adults: passengerCounts.adults,
          children: passengerCounts.children,
          infants: passengerCounts.infants,
          cabin: getCabinCode(travelClass),
          tripType: tripType as 'oneWay' | 'roundTrip',
          fareType: fareType as 'ON' | 'OFF',
          isDirect: false // Can be made configurable
        };

        console.log('🚀 Searching flights with data:', searchFormData);

        // Search flights using the service with search-list integration
        const { searchResponse, searchListResponse } = await flightService.searchFlightsWithFormDataAndSearchList(searchFormData);

        console.log('📡 Flight service response received:', {
          success: searchResponse.success,
          message: searchResponse.message,
          onwardFlightsCount: searchResponse.data?.onwardFlights?.length || 0,
          returnFlightsCount: searchResponse.data?.returnFlights?.length || 0,
          totalResults: searchResponse.data?.totalResults,
          hasTUI: !!searchResponse.data?.tui,
          hasSearchListResponse: !!searchListResponse
        });

        if (searchResponse.success && searchResponse.data) {
          let onwardFlights = searchResponse.data.onwardFlights;
          let returnFlights = searchResponse.data.returnFlights;

          // If we have search-list response, use those flights instead
          if (searchListResponse && searchListResponse.Trips && searchListResponse.Trips.length > 0) {
            console.log('✅ Using search-list response for flight data');
            console.log('SearchListResponse structure:', {
              completed: searchListResponse.Completed,
              tripsCount: searchListResponse.Trips.length,
              firstTripJourneyCount: searchListResponse.Trips[0]?.Journey?.length || 0
            });

            const searchListFlights = transformSearchListResponseToFlights(searchListResponse);
            console.log('🔄 Transformed flights from search-list:', {
              count: searchListFlights.length,
              firstFlight: searchListFlights[0] ? {
                id: searchListFlights[0].id,
                airline: searchListFlights[0].airline,
                flightNumber: searchListFlights[0].flightNumber,
                price: searchListFlights[0].price,
                departure: searchListFlights[0].departure,
                arrival: searchListFlights[0].arrival
              } : null
            });

            onwardFlights = searchListFlights;

            // For round trip, we might need to separate onward and return flights
            // This depends on how the API returns round trip data
            if (isRoundTrip) {
              // For now, use the same flights for return - this might need adjustment based on API behavior
              returnFlights = searchListFlights;
            }
          } else {
            console.log('ℹ️ No search-list response or no trips found, using original search results');
          }

          console.log('Setting flights state:', {
            onwardFlightsCount: onwardFlights.length,
            returnFlightsCount: returnFlights?.length || 0,
            isRoundTrip,
            sampleOnwardFlight: onwardFlights[0] ? {
              id: onwardFlights[0].id,
              price: onwardFlights[0].price,
              airline: onwardFlights[0].airline
            } : null
          });



          // For now, use empty arrays for new components until we get proper API data
          setOneWayFlights([]);
          setDomesticOnwardFlights([]);
          setDomesticReturnFlights([]);
          setInternationalOnwardFlights([]);
          setInternationalReturnFlights([]);

          // Update price range filter to accommodate the actual flight prices
          if (onwardFlights.length > 0) {
            const allFlights = isRoundTrip && returnFlights ? [...onwardFlights, ...returnFlights] : onwardFlights;
            const minPrice = Math.min(...allFlights.map(f => f.price));
            const maxPrice = Math.max(...allFlights.map(f => f.price));

            console.log('Updating price range filter:', {
              minPrice,
              maxPrice,
              currentRange: filters.priceRange
            });

            // Only update if the current range doesn't accommodate all flights
            if (minPrice < filters.priceRange[0] || maxPrice > filters.priceRange[1]) {
              setFilters(prev => ({
                ...prev,
                priceRange: [Math.min(minPrice, prev.priceRange[0]), Math.max(maxPrice, prev.priceRange[1])]
              }));
            }
          }

          // Log the data source for debugging
          console.log('Flight data source:', {
            flightCount: onwardFlights.length,
            hasTUI: !!searchResponse.data.tui,
            searchMessage: searchResponse.message
          });
        } else {
          throw new Error(searchResponse.message || 'Failed to search flights');
        }

      } catch (error) {
        console.error('Error searching flights:', error);
        setError(error instanceof Error ? error.message : 'Failed to search flights');

        // Fallback to JSON data for development
        console.log('Falling back to JSON data...');

        if (isRoundTrip) {
          try {
            // Load round trip data from JSON files
            const roundTripData = await loadRoundTripData(from, to);

            if (roundTripData.success && roundTripData.data) {
              // Transform onward flights
              const transformedOnwardFlights = roundTripData.data.onwardFlights || [];

              // Transform return flights
              const transformedReturnFlights = roundTripData.data.returnFlights || [];

              console.log(`✅ Loaded ${transformedOnwardFlights.length} onward and ${transformedReturnFlights.length} return flights from JSON`);

              // Set the appropriate state variables based on route type
              if (isInternationalRoute(from, to)) {
                setInternationalOnwardFlights(transformedOnwardFlights as FlightOptionInternational[]);
                setInternationalReturnFlights(transformedReturnFlights as FlightOptionInternational[]);
                setDomesticOnwardFlights([]);
                setDomesticReturnFlights([]);
              } else {
                setDomesticOnwardFlights(transformedOnwardFlights as FlightOption[]);
                setDomesticReturnFlights(transformedReturnFlights as FlightOption[]);
                setInternationalOnwardFlights([]);
                setInternationalReturnFlights([]);
              }

              setDataSource(`json-${roundTripData.data.searchInfo?.flightType || 'unknown'}`);
            } else {
              throw new Error('Failed to load round trip JSON data');
            }
          } catch (jsonError) {
            console.error('Error loading JSON data, falling back to hardcoded mock data:', jsonError);
            // Final fallback to hardcoded mock data
            setDataSource('mock-hardcoded');
          }
        } else {
          // For one-way trips, load JSON data
          try {
            const oneWayData = await loadOneWayData();

            if (oneWayData.success && oneWayData.data) {
              const transformedFlights = oneWayData.data.flights || [];
              console.log(`✅ Loaded ${transformedFlights.length} one-way flights from JSON`);

              setOneWayFlights(transformedFlights as FlightOptionOneWay[]);
              setDomesticOnwardFlights([]);
              setDomesticReturnFlights([]);
              setInternationalOnwardFlights([]);
              setInternationalReturnFlights([]);

              setDataSource('json-oneway');
            } else {
              throw new Error('Failed to load one-way JSON data');
            }
          } catch (jsonError) {
            console.error('Error loading one-way JSON data, falling back to mock data:', jsonError);
            setDataSource('mock-oneway');
          }
        }
      } finally {
        setLoading(false);
      }
    };

    searchFlights();
  }, [from, to, departDate, returnDate, travelers, travelClass, tripType, fareType, isRoundTrip]);





  const handleModifySearch = () => {
    setShowModifyPopup(true);
  };

  const handleCloseModifyPopup = () => {
    setShowModifyPopup(false);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="pt-20 pb-16 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              <div className="lg:col-span-1">
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <div className="animate-pulse space-y-4">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                    <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                  </div>
                </div>
              </div>
              <div className="lg:col-span-3 space-y-4">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="bg-white rounded-lg shadow-sm p-6">
                    <div className="animate-pulse">
                      <div className="flex items-center space-x-4">
                        <div className="h-12 w-16 bg-gray-200 rounded"></div>
                        <div className="flex-1 space-y-2">
                          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                          <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                        </div>
                        <div className="h-8 w-24 bg-gray-200 rounded"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="pt-20 pb-16 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="bg-white rounded-lg shadow-sm p-8 text-center">
              <div className="mb-4">
                <i className="ri-error-warning-line text-4xl text-red-500"></i>
              </div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Flight Search Error</h2>
              <p className="text-gray-600 mb-6">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 relative">
      <Header />

      <div className="pt-20 pb-32 px-4">
        <div className="max-w-7xl mx-auto">
          {/* Search Summary */}
          <div className="bg-white rounded-lg shadow-sm p-4 md:p-6 mb-6">
            <div className="flex items-center justify-between flex-wrap gap-4">
              <div>
                <h1 className="text-xl md:text-2xl font-bold text-gray-900 mb-2">
                  {from} → {to} {isRoundTrip && '• Round Trip'}
                </h1>
                <div className="flex items-center space-x-4 text-sm text-gray-600">
                  <span>Depart: {departDate}</span>
                  {returnDate && <span>• Return: {returnDate}</span>}
                  <span>• {travelers} Traveller{parseInt(travelers) > 1 ? 's' : ''}</span>
                  <span>• {travelClass.charAt(0).toUpperCase() + travelClass.slice(1)}</span>
                  {process.env.NODE_ENV === 'development' && (
                    <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                      Data: {dataSource}
                    </span>
                  )}
                </div>
              </div>
              <button 
                onClick={handleModifySearch}
                className="text-[#013688] font-semibold hover:text-blue-700 transition-colors"
              >
                Modify Search
              </button>
            </div>
          </div>

          <div className="w-full">
            {/* Flight Component Rendering */}
            <div className="space-y-6">
              {/* One Way Flights */}
              {!isRoundTrip && (
                <OneWayList
                  flights={oneWayFlights}
                  loading={loading}
                  error={error}
                />
              )}

              {/* Round Trip Domestic Flights */}
              {isRoundTrip && !isInternationalRoute(from, to) && (
                <RoundTripDomesticList
                  onwardFlights={domesticOnwardFlights}
                  returnFlights={domesticReturnFlights}
                  loading={loading}
                  error={error}
                />
              )}

              {/* Round Trip International Flights */}
              {isRoundTrip && isInternationalRoute(from, to) && (
                <RoundTripInternationalList
                  onwardFlights={internationalOnwardFlights}
                  returnFlights={internationalReturnFlights}
                  loading={loading}
                  error={error}
                />
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Modify Search Popup */}


      {/* Modify Search Popup */}
      {showModifyPopup && (
        <div className="fixed inset-0 bg-black/50 z-[100] flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-y-auto">
            {/* Popup Header */}
            <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
              <h2 className="text-xl font-bold text-gray-900">Modify Flight Search</h2>
              <button 
                onClick={handleCloseModifyPopup}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              >
                <i className="ri-close-line text-xl text-gray-500"></i>
              </button>
            </div>

            {/* Flight Search Component */}
            <div>
              <FlightSearch isModify={true} />
            </div>
          </div>
        </div>
      )}

      <Footer />
    </div>
  );
}

export default function FlightsPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="pt-20 pb-16 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    }>
      <FlightSearchResults />
    </Suspense>
  );
}
